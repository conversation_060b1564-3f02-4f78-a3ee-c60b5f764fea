# LibyanoEx Database Setup

This directory contains all the database schema, policies, and setup scripts for the LibyanoEx web application system.

## Files Overview

- `schema.sql` - Main database schema with tables, indexes, and triggers
- `rls-policies.sql` - Row Level Security policies for data access control
- `functions.sql` - Custom database functions for business logic
- `seed-data.sql` - Initial seed data for development and testing

## Setup Instructions

### 1. Supabase Setup

1. Create a new Supabase project at [supabase.com](https://supabase.com)
2. Go to Settings > API to get your project URL and anon key
3. Update your `.env.local` file with the Supabase credentials:

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
```

### 2. Database Schema Setup

Execute the SQL files in the following order:

1. **Schema Creation**
   ```sql
   -- Run schema.sql in Supabase SQL Editor
   -- This creates all tables, indexes, and triggers
   ```

2. **Functions**
   ```sql
   -- Run functions.sql in Supabase SQL Editor
   -- This creates utility functions for business logic
   ```

3. **Row Level Security**
   ```sql
   -- Run rls-policies.sql in Supabase SQL Editor
   -- This sets up security policies for data access
   ```

4. **Seed Data**
   ```sql
   -- Run seed-data.sql in Supabase SQL Editor
   -- This populates initial settings and configuration
   ```

### 3. Authentication Setup

Supabase Auth is automatically configured. The system supports:

- Email/password authentication
- Social login (can be configured in Supabase dashboard)
- Role-based access control (admin, manager, user, client)

### 4. Storage Setup (Optional)

If you need file uploads:

1. Go to Storage in Supabase dashboard
2. Create buckets for:
   - `avatars` - User profile pictures
   - `documents` - Transaction documents
   - `reports` - Generated reports

### 5. Real-time Setup (Optional)

For real-time features:

1. Go to Database > Replication in Supabase
2. Enable replication for tables that need real-time updates:
   - `transactions`
   - `notifications`
   - `accounts`

## Database Schema Overview

### Core Tables

- **profiles** - User profiles extending Supabase auth
- **transactions** - All financial transactions
- **accounts** - User accounts/wallets
- **transaction_logs** - Audit trail for transactions
- **notifications** - User notifications
- **settings** - Application configuration

### Key Features

- **UUID Primary Keys** - All tables use UUIDs for better security
- **Row Level Security** - Users can only access their own data
- **Audit Trails** - All changes are logged
- **Soft Deletes** - Important data is never permanently deleted
- **Timestamps** - All records have created_at and updated_at
- **Indexes** - Optimized for common query patterns

### Business Rules

- Users can only see their own transactions and accounts
- Admins and managers have elevated permissions
- All financial operations are logged
- Account balances cannot go negative
- Reference numbers are unique and auto-generated

## Development Notes

- Use the Supabase client libraries for database operations
- All database operations should go through the API layer
- Test with the seed data before deploying to production
- Monitor query performance and add indexes as needed
- Regular backups are configured automatically by Supabase

## Security Considerations

- Row Level Security is enabled on all tables
- Sensitive settings are not publicly accessible
- All user inputs should be validated before database operations
- Use parameterized queries to prevent SQL injection
- Regular security audits of policies and permissions
