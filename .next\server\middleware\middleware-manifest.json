{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_e76d35f6._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_4c46a49c.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "ht1zTrpfTwVvB5hOCi+QcjAMGgvXfOMeFrMILLnNBaY=", "__NEXT_PREVIEW_MODE_ID": "42e3d1db91022c11da72a79bed78e1b4", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "2ff458c753e5f8e18114328086c86ebf212452c71ec1cb7f35fbe46f58be14cc", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "ec47e0be3f0c85ddcdb1858df25e55780c43e4764f61f41cd817af21e252a927"}}}, "instrumentation": null, "functions": {}}