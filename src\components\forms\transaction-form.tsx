'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { TransactionType } from '@/types'
import { generateReferenceNumber } from '@/lib/utils'
import { Loader2 } from 'lucide-react'

const transactionSchema = z.object({
  type: z.enum(['deposit', 'withdrawal', 'transfer', 'exchange']),
  amount: z.number().min(0.01, 'Amount must be greater than 0'),
  currency: z.string().min(1, 'Currency is required'),
  description: z.string().optional(),
  toAccount: z.string().optional(),
})

type TransactionFormData = z.infer<typeof transactionSchema>

interface TransactionFormProps {
  onSubmit: (data: TransactionFormData & { referenceNumber: string }) => Promise<void>
  onCancel?: () => void
  defaultValues?: Partial<TransactionFormData>
}

const transactionTypes = [
  { value: 'deposit', label: 'Deposit' },
  { value: 'withdrawal', label: 'Withdrawal' },
  { value: 'transfer', label: 'Transfer' },
  { value: 'exchange', label: 'Exchange' },
]

const currencies = [
  { value: 'USD', label: 'US Dollar (USD)' },
  { value: 'EUR', label: 'Euro (EUR)' },
  { value: 'GBP', label: 'British Pound (GBP)' },
  { value: 'LYD', label: 'Libyan Dinar (LYD)' },
]

export default function TransactionForm({ onSubmit, onCancel, defaultValues }: TransactionFormProps) {
  const [isLoading, setIsLoading] = useState(false)

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<TransactionFormData>({
    resolver: zodResolver(transactionSchema),
    defaultValues: {
      currency: 'USD',
      ...defaultValues,
    },
  })

  const selectedType = watch('type')

  const handleFormSubmit = async (data: TransactionFormData) => {
    setIsLoading(true)
    try {
      const referenceNumber = generateReferenceNumber()
      await onSubmit({ ...data, referenceNumber })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle>New Transaction</CardTitle>
        <CardDescription>
          Create a new transaction for your account
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          {/* Transaction Type */}
          <div className="space-y-2">
            <label htmlFor="type" className="text-sm font-medium">
              Transaction Type
            </label>
            <select
              id="type"
              {...register('type')}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">Select transaction type</option>
              {transactionTypes.map((type) => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>
            {errors.type && (
              <p className="text-sm text-red-500">{errors.type.message}</p>
            )}
          </div>

          {/* Amount */}
          <div className="space-y-2">
            <label htmlFor="amount" className="text-sm font-medium">
              Amount
            </label>
            <Input
              id="amount"
              type="number"
              step="0.01"
              placeholder="0.00"
              {...register('amount', { valueAsNumber: true })}
              className={errors.amount ? 'border-red-500' : ''}
            />
            {errors.amount && (
              <p className="text-sm text-red-500">{errors.amount.message}</p>
            )}
          </div>

          {/* Currency */}
          <div className="space-y-2">
            <label htmlFor="currency" className="text-sm font-medium">
              Currency
            </label>
            <select
              id="currency"
              {...register('currency')}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              {currencies.map((currency) => (
                <option key={currency.value} value={currency.value}>
                  {currency.label}
                </option>
              ))}
            </select>
            {errors.currency && (
              <p className="text-sm text-red-500">{errors.currency.message}</p>
            )}
          </div>

          {/* To Account (for transfers) */}
          {selectedType === 'transfer' && (
            <div className="space-y-2">
              <label htmlFor="toAccount" className="text-sm font-medium">
                To Account
              </label>
              <Input
                id="toAccount"
                type="text"
                placeholder="Enter destination account number"
                {...register('toAccount')}
                className={errors.toAccount ? 'border-red-500' : ''}
              />
              {errors.toAccount && (
                <p className="text-sm text-red-500">{errors.toAccount.message}</p>
              )}
            </div>
          )}

          {/* Description */}
          <div className="space-y-2">
            <label htmlFor="description" className="text-sm font-medium">
              Description (Optional)
            </label>
            <Input
              id="description"
              type="text"
              placeholder="Enter transaction description"
              {...register('description')}
            />
          </div>

          {/* Form Actions */}
          <div className="flex space-x-4">
            <Button type="submit" disabled={isLoading} className="flex-1">
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : (
                'Create Transaction'
              )}
            </Button>
            {onCancel && (
              <Button type="button" variant="outline" onClick={onCancel}>
                Cancel
              </Button>
            )}
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
