'use client'

import { useState } from 'react'
import DashboardLayout from '@/components/layout/dashboard-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { formatCurrency } from '@/lib/utils'
import { 
  Download, 
  Calendar, 
  TrendingUp, 
  TrendingDown,
  DollarSign,
  Users,
  CreditCard,
  BarChart3
} from 'lucide-react'

// Mock data for charts and reports
const monthlyData = [
  { month: 'Jan', revenue: 45000, transactions: 120, users: 15 },
  { month: 'Feb', revenue: 52000, transactions: 145, users: 22 },
  { month: 'Mar', revenue: 48000, transactions: 132, users: 18 },
  { month: 'Apr', revenue: 61000, transactions: 167, users: 28 },
  { month: 'May', revenue: 55000, transactions: 154, users: 25 },
  { month: 'Jun', revenue: 67000, transactions: 189, users: 32 },
]

const reportTypes = [
  {
    id: 'transaction-summary',
    title: 'Transaction Summary',
    description: 'Overview of all transactions by type and status',
    icon: CreditCard,
  },
  {
    id: 'revenue-analysis',
    title: 'Revenue Analysis',
    description: 'Detailed revenue breakdown and trends',
    icon: DollarSign,
  },
  {
    id: 'user-activity',
    title: 'User Activity Report',
    description: 'User engagement and activity metrics',
    icon: Users,
  },
  {
    id: 'performance-metrics',
    title: 'Performance Metrics',
    description: 'System performance and operational metrics',
    icon: BarChart3,
  },
]

export default function ReportsPage() {
  const [dateRange, setDateRange] = useState({
    from: '2025-01-01',
    to: '2025-01-31'
  })

  const handleGenerateReport = (reportType: string) => {
    console.log(`Generating ${reportType} report for ${dateRange.from} to ${dateRange.to}`)
    // In real app, this would call the API to generate the report
  }

  const handleExportData = (format: string) => {
    console.log(`Exporting data in ${format} format`)
    // In real app, this would trigger a download
  }

  // Calculate summary statistics
  const totalRevenue = monthlyData.reduce((sum, month) => sum + month.revenue, 0)
  const totalTransactions = monthlyData.reduce((sum, month) => sum + month.transactions, 0)
  const totalUsers = monthlyData.reduce((sum, month) => sum + month.users, 0)
  const avgMonthlyRevenue = totalRevenue / monthlyData.length

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Reports & Analytics
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Generate reports and analyze business performance
            </p>
          </div>
          <div className="flex space-x-2">
            <Button variant="outline" onClick={() => handleExportData('csv')}>
              <Download className="mr-2 h-4 w-4" />
              Export CSV
            </Button>
            <Button variant="outline" onClick={() => handleExportData('pdf')}>
              <Download className="mr-2 h-4 w-4" />
              Export PDF
            </Button>
          </div>
        </div>

        {/* Date Range Selector */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calendar className="mr-2 h-5 w-5" />
              Report Period
            </CardTitle>
            <CardDescription>
              Select the date range for your reports
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <label htmlFor="from-date" className="block text-sm font-medium mb-2">
                  From Date
                </label>
                <Input
                  id="from-date"
                  type="date"
                  value={dateRange.from}
                  onChange={(e) => setDateRange(prev => ({ ...prev, from: e.target.value }))}
                />
              </div>
              <div className="flex-1">
                <label htmlFor="to-date" className="block text-sm font-medium mb-2">
                  To Date
                </label>
                <Input
                  id="to-date"
                  type="date"
                  value={dateRange.to}
                  onChange={(e) => setDateRange(prev => ({ ...prev, to: e.target.value }))}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Summary Statistics */}
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(totalRevenue)}</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-600 flex items-center">
                  <TrendingUp className="mr-1 h-3 w-3" />
                  +12.5%
                </span>
                from last period
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Transactions</CardTitle>
              <CreditCard className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalTransactions.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-600 flex items-center">
                  <TrendingUp className="mr-1 h-3 w-3" />
                  +8.2%
                </span>
                from last period
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">New Users</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalUsers}</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-red-600 flex items-center">
                  <TrendingDown className="mr-1 h-3 w-3" />
                  -2.1%
                </span>
                from last period
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Monthly Revenue</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(avgMonthlyRevenue)}</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-600 flex items-center">
                  <TrendingUp className="mr-1 h-3 w-3" />
                  +5.7%
                </span>
                from last period
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Monthly Performance Chart */}
        <Card>
          <CardHeader>
            <CardTitle>Monthly Performance</CardTitle>
            <CardDescription>
              Revenue and transaction trends over the past 6 months
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-64 flex items-end justify-between space-x-2">
              {monthlyData.map((month, index) => (
                <div key={month.month} className="flex flex-col items-center space-y-2 flex-1">
                  <div className="w-full bg-gray-200 rounded-t-lg relative">
                    <div 
                      className="bg-blue-600 rounded-t-lg transition-all duration-300 hover:bg-blue-700"
                      style={{ 
                        height: `${(month.revenue / Math.max(...monthlyData.map(m => m.revenue))) * 200}px`,
                        minHeight: '20px'
                      }}
                      title={`Revenue: ${formatCurrency(month.revenue)}`}
                    />
                  </div>
                  <span className="text-sm font-medium">{month.month}</span>
                  <span className="text-xs text-gray-500">{formatCurrency(month.revenue)}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Available Reports */}
        <Card>
          <CardHeader>
            <CardTitle>Available Reports</CardTitle>
            <CardDescription>
              Generate detailed reports for different aspects of your business
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              {reportTypes.map((report) => {
                const Icon = report.icon
                return (
                  <div
                    key={report.id}
                    className="p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-3">
                        <div className="p-2 bg-blue-100 rounded-lg">
                          <Icon className="h-5 w-5 text-blue-600" />
                        </div>
                        <div>
                          <h3 className="font-medium">{report.title}</h3>
                          <p className="text-sm text-gray-500 mt-1">{report.description}</p>
                        </div>
                      </div>
                      <Button 
                        size="sm" 
                        onClick={() => handleGenerateReport(report.id)}
                      >
                        Generate
                      </Button>
                    </div>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
