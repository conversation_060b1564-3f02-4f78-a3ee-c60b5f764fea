-- Row Level Security Policies for LibyanoEx
-- These policies ensure users can only access their own data and admins have full access

-- Enable RLS on all tables
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.transaction_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.settings ENABLE ROW LEVEL SECURITY;

-- Helper function to get current user's role
CREATE OR REPLACE FUNCTION get_user_role()
RETURNS TEXT AS $$
BEGIN
    RETURN (
        SELECT role 
        FROM public.profiles 
        WHERE id = auth.uid()
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Profiles policies
CREATE POLICY "Users can view their own profile" ON public.profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON public.profiles
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Admins can view all profiles" ON public.profiles
    FOR SELECT USING (get_user_role() = 'admin');

CREATE POLICY "Admins can update all profiles" ON public.profiles
    FOR UPDATE USING (get_user_role() = 'admin');

CREATE POLICY "Admins can insert profiles" ON public.profiles
    FOR INSERT WITH CHECK (get_user_role() = 'admin');

CREATE POLICY "Admins can delete profiles" ON public.profiles
    FOR DELETE USING (get_user_role() = 'admin');

-- Transactions policies
CREATE POLICY "Users can view their own transactions" ON public.transactions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own transactions" ON public.transactions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Admins and managers can view all transactions" ON public.transactions
    FOR SELECT USING (get_user_role() IN ('admin', 'manager'));

CREATE POLICY "Admins and managers can update all transactions" ON public.transactions
    FOR UPDATE USING (get_user_role() IN ('admin', 'manager'));

CREATE POLICY "Admins can delete transactions" ON public.transactions
    FOR DELETE USING (get_user_role() = 'admin');

-- Accounts policies
CREATE POLICY "Users can view their own accounts" ON public.accounts
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own accounts" ON public.accounts
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own accounts" ON public.accounts
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Admins can view all accounts" ON public.accounts
    FOR SELECT USING (get_user_role() = 'admin');

CREATE POLICY "Admins can update all accounts" ON public.accounts
    FOR UPDATE USING (get_user_role() = 'admin');

CREATE POLICY "Admins can delete accounts" ON public.accounts
    FOR DELETE USING (get_user_role() = 'admin');

-- Transaction logs policies
CREATE POLICY "Users can view logs for their transactions" ON public.transaction_logs
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.transactions 
            WHERE id = transaction_id AND user_id = auth.uid()
        )
    );

CREATE POLICY "Admins and managers can view all transaction logs" ON public.transaction_logs
    FOR SELECT USING (get_user_role() IN ('admin', 'manager'));

CREATE POLICY "Admins and managers can insert transaction logs" ON public.transaction_logs
    FOR INSERT WITH CHECK (get_user_role() IN ('admin', 'manager'));

-- Notifications policies
CREATE POLICY "Users can view their own notifications" ON public.notifications
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own notifications" ON public.notifications
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Admins can view all notifications" ON public.notifications
    FOR SELECT USING (get_user_role() = 'admin');

CREATE POLICY "Admins and managers can insert notifications" ON public.notifications
    FOR INSERT WITH CHECK (get_user_role() IN ('admin', 'manager'));

CREATE POLICY "Admins can delete notifications" ON public.notifications
    FOR DELETE USING (get_user_role() = 'admin');

-- Settings policies
CREATE POLICY "Everyone can view public settings" ON public.settings
    FOR SELECT USING (is_public = true);

CREATE POLICY "Admins can view all settings" ON public.settings
    FOR SELECT USING (get_user_role() = 'admin');

CREATE POLICY "Admins can manage all settings" ON public.settings
    FOR ALL USING (get_user_role() = 'admin');
