'use client'

import { useState } from 'react'
import DashboardLayout from '@/components/layout/dashboard-layout'
import TransactionForm from '@/components/forms/transaction-form'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { formatCurrency, formatDateTime } from '@/lib/utils'
import { 
  Plus, 
  Search, 
  Filter, 
  ArrowUpRight, 
  ArrowDownRight, 
  CreditCard,
  Download
} from 'lucide-react'

// Mock data - in real app, this would come from API
const mockTransactions = [
  {
    id: '1',
    referenceNumber: 'LBX-2025011001-ABC123',
    type: 'deposit',
    amount: 1500.00,
    currency: 'USD',
    description: 'Salary deposit',
    status: 'completed',
    createdAt: '2025-01-10T10:00:00Z',
    processedAt: '2025-01-10T10:05:00Z',
  },
  {
    id: '2',
    referenceNumber: 'LBX-**********-DEF456',
    type: 'withdrawal',
    amount: 250.00,
    currency: 'USD',
    description: 'ATM withdrawal',
    status: 'completed',
    createdAt: '2025-01-09T14:30:00Z',
    processedAt: '2025-01-09T14:31:00Z',
  },
  {
    id: '3',
    referenceNumber: 'LBX-**********-GHI789',
    type: 'transfer',
    amount: 500.00,
    currency: 'USD',
    description: 'Transfer to savings account',
    status: 'pending',
    createdAt: '2025-01-08T16:45:00Z',
    processedAt: null,
  },
  {
    id: '4',
    referenceNumber: 'LBX-**********-JKL012',
    type: 'exchange',
    amount: 1000.00,
    currency: 'EUR',
    description: 'Currency exchange USD to EUR',
    status: 'completed',
    createdAt: '2025-01-07T11:20:00Z',
    processedAt: '2025-01-07T11:25:00Z',
  },
  {
    id: '5',
    referenceNumber: 'LBX-**********-MNO345',
    type: 'deposit',
    amount: 2000.00,
    currency: 'USD',
    description: 'Business payment received',
    status: 'failed',
    createdAt: '2025-01-06T09:15:00Z',
    processedAt: null,
  },
]

export default function TransactionsPage() {
  const [showForm, setShowForm] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')

  const handleCreateTransaction = async (data: any) => {
    console.log('Creating transaction:', data)
    // In real app, this would call the API
    setShowForm(false)
    // Show success message
  }

  const filteredTransactions = mockTransactions.filter(transaction => {
    const matchesSearch = transaction.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         transaction.referenceNumber.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === 'all' || transaction.status === statusFilter
    return matchesSearch && matchesStatus
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'failed':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'deposit':
        return <ArrowDownRight className="h-4 w-4 text-green-600" />
      case 'withdrawal':
        return <ArrowUpRight className="h-4 w-4 text-red-600" />
      case 'transfer':
        return <CreditCard className="h-4 w-4 text-blue-600" />
      case 'exchange':
        return <CreditCard className="h-4 w-4 text-purple-600" />
      default:
        return <CreditCard className="h-4 w-4 text-gray-600" />
    }
  }

  if (showForm) {
    return (
      <DashboardLayout>
        <div className="max-w-4xl mx-auto">
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              New Transaction
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Create a new transaction for your account
            </p>
          </div>
          <TransactionForm
            onSubmit={handleCreateTransaction}
            onCancel={() => setShowForm(false)}
          />
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Transactions
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Manage and view all your transactions
            </p>
          </div>
          <Button onClick={() => setShowForm(true)}>
            <Plus className="mr-2 h-4 w-4" />
            New Transaction
          </Button>
        </div>

        {/* Filters and Search */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search transactions..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">All Status</option>
                  <option value="completed">Completed</option>
                  <option value="pending">Pending</option>
                  <option value="failed">Failed</option>
                </select>
                <Button variant="outline" size="icon">
                  <Filter className="h-4 w-4" />
                </Button>
                <Button variant="outline" size="icon">
                  <Download className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Transactions List */}
        <Card>
          <CardHeader>
            <CardTitle>Transaction History</CardTitle>
            <CardDescription>
              {filteredTransactions.length} transactions found
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {filteredTransactions.map((transaction) => (
                <div
                  key={transaction.id}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                >
                  <div className="flex items-center space-x-4">
                    <div className="p-2 rounded-full bg-gray-100 dark:bg-gray-800">
                      {getTransactionIcon(transaction.type)}
                    </div>
                    <div>
                      <p className="font-medium">{transaction.description}</p>
                      <p className="text-sm text-gray-500">
                        {transaction.referenceNumber}
                      </p>
                      <p className="text-sm text-gray-500">
                        {formatDateTime(transaction.createdAt)}
                      </p>
                    </div>
                  </div>
                  <div className="text-right space-y-1">
                    <p className={`font-medium ${
                      transaction.type === 'deposit' 
                        ? 'text-green-600' 
                        : 'text-gray-900 dark:text-white'
                    }`}>
                      {transaction.type === 'deposit' ? '+' : '-'}
                      {formatCurrency(transaction.amount, transaction.currency)}
                    </p>
                    <Badge className={getStatusColor(transaction.status)}>
                      {transaction.status}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
            
            {filteredTransactions.length === 0 && (
              <div className="text-center py-8">
                <p className="text-gray-500 dark:text-gray-400">
                  No transactions found matching your criteria.
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
