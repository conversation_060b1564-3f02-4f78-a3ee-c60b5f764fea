-- Seed data for LibyanoEx development and testing
-- This file contains sample data for development purposes

-- Insert default settings
INSERT INTO public.settings (key, value, description, is_public) VALUES
('app_name', '"LibyanoEx"', 'Application name', true),
('app_version', '"1.0.0"', 'Application version', true),
('maintenance_mode', 'false', 'Maintenance mode flag', false),
('max_transaction_amount', '100000.00', 'Maximum transaction amount', false),
('supported_currencies', '["USD", "EUR", "GBP", "LYD"]', 'Supported currencies', true),
('transaction_fee_percentage', '0.025', 'Transaction fee percentage (2.5%)', false),
('email_notifications', 'true', 'Enable email notifications', false),
('sms_notifications', 'false', 'Enable SMS notifications', false);

-- Note: User profiles will be created automatically when users sign up through Supabase Auth
-- The following are example profiles that would be created after user registration

-- Example transaction reference numbers and sample data structure
-- These would be inserted after users are created

-- Sample account types and currencies
INSERT INTO public.settings (key, value, description, is_public) VALUES
('account_types', '["checking", "savings", "business", "crypto"]', 'Available account types', true),
('transaction_types', '["deposit", "withdrawal", "transfer", "exchange", "fee"]', 'Available transaction types', true),
('transaction_statuses', '["pending", "completed", "failed", "cancelled"]', 'Transaction status options', true),
('user_roles', '["admin", "manager", "user", "client"]', 'Available user roles', false);

-- Default notification templates
INSERT INTO public.settings (key, value, description, is_public) VALUES
('notification_templates', '{
  "transaction_completed": {
    "title": "Transaction Completed",
    "message": "Your transaction {reference_number} has been completed successfully."
  },
  "transaction_failed": {
    "title": "Transaction Failed", 
    "message": "Your transaction {reference_number} has failed. Please contact support."
  },
  "account_created": {
    "title": "Account Created",
    "message": "Your new {account_type} account has been created successfully."
  },
  "low_balance": {
    "title": "Low Balance Alert",
    "message": "Your account balance is below the minimum threshold."
  }
}', 'Notification message templates', false);

-- Business rules and limits
INSERT INTO public.settings (key, value, description, is_public) VALUES
('daily_transaction_limit', '50000.00', 'Daily transaction limit per user', false),
('monthly_transaction_limit', '500000.00', 'Monthly transaction limit per user', false),
('minimum_balance', '10.00', 'Minimum account balance', false),
('account_creation_fee', '25.00', 'Fee for creating new accounts', true),
('currency_exchange_markup', '0.015', 'Currency exchange markup (1.5%)', false);

-- System configuration
INSERT INTO public.settings (key, value, description, is_public) VALUES
('session_timeout', '3600', 'Session timeout in seconds (1 hour)', false),
('password_min_length', '8', 'Minimum password length', true),
('require_2fa', 'false', 'Require two-factor authentication', false),
('backup_frequency', '24', 'Database backup frequency in hours', false),
('log_retention_days', '90', 'Number of days to retain logs', false);

-- Feature flags
INSERT INTO public.settings (key, value, description, is_public) VALUES
('enable_crypto_accounts', 'true', 'Enable cryptocurrency accounts', true),
('enable_international_transfers', 'true', 'Enable international transfers', true),
('enable_mobile_app', 'false', 'Enable mobile app features', true),
('enable_api_access', 'true', 'Enable API access for integrations', false),
('enable_real_time_notifications', 'true', 'Enable real-time notifications', true);

-- Sample business data (these would be populated after user registration)
-- Reference number format: LBX-{timestamp}-{random}
-- Account number format: {type_prefix}{user_id_short}{random_digits}

-- Example of how data would look after users are created:
-- 
-- Sample profile (created after Supabase auth):
-- INSERT INTO public.profiles (id, email, full_name, role, company, phone) VALUES
-- ('550e8400-e29b-41d4-a716-************', '<EMAIL>', 'System Administrator', 'admin', 'LibyanoEx', '+**********');
--
-- Sample account:
-- INSERT INTO public.accounts (user_id, account_number, account_type, currency, balance) VALUES
-- ('550e8400-e29b-41d4-a716-************', 'CHK550E84001234', 'checking', 'USD', 10000.00);
--
-- Sample transaction:
-- INSERT INTO public.transactions (user_id, type, amount, currency, status, description, reference_number) VALUES
-- ('550e8400-e29b-41d4-a716-************', 'deposit', 5000.00, 'USD', 'completed', 'Initial deposit', 'LBX-**********-ABC123');
