{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr'\nimport { NextResponse, type NextRequest } from 'next/server'\n\nexport async function middleware(request: NextRequest) {\n  let response = NextResponse.next({\n    request: {\n      headers: request.headers,\n    },\n  })\n\n  const supabase = createServerClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\n    {\n      cookies: {\n        getAll() {\n          return request.cookies.getAll()\n        },\n        setAll(cookiesToSet) {\n          cookiesToSet.forEach(({ name, value }) => request.cookies.set(name, value))\n          response = NextResponse.next({\n            request,\n          })\n          cookiesToSet.forEach(({ name, value, options }) =>\n            response.cookies.set(name, value, options)\n          )\n        },\n      },\n    }\n  )\n\n  // Refresh session if expired - required for Server Components\n  await supabase.auth.getUser()\n\n  const {\n    data: { user },\n  } = await supabase.auth.getUser()\n\n  // Protected routes that require authentication\n  const protectedRoutes = ['/dashboard', '/admin', '/transactions', '/users', '/reports']\n  const authRoutes = ['/auth/login', '/auth/register']\n  \n  const isProtectedRoute = protectedRoutes.some(route => \n    request.nextUrl.pathname.startsWith(route)\n  )\n  const isAuthRoute = authRoutes.some(route => \n    request.nextUrl.pathname.startsWith(route)\n  )\n\n  // Redirect to login if accessing protected route without authentication\n  if (isProtectedRoute && !user) {\n    const redirectUrl = new URL('/auth/login', request.url)\n    redirectUrl.searchParams.set('redirectTo', request.nextUrl.pathname)\n    return NextResponse.redirect(redirectUrl)\n  }\n\n  // Redirect to dashboard if accessing auth routes while authenticated\n  if (isAuthRoute && user) {\n    return NextResponse.redirect(new URL('/dashboard', request.url))\n  }\n\n  // Check role-based access for admin routes\n  if (request.nextUrl.pathname.startsWith('/admin') && user) {\n    const { data: profile } = await supabase\n      .from('profiles')\n      .select('role')\n      .eq('id', user.id)\n      .single()\n\n    if (!profile || !['admin', 'manager'].includes(profile.role)) {\n      return NextResponse.redirect(new URL('/dashboard', request.url))\n    }\n  }\n\n  return response\n}\n\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * - public folder\n     */\n    '/((?!_next/static|_next/image|favicon.ico|.*\\\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',\n  ],\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AAAA;;;AAEO,eAAe,WAAW,OAAoB;IACnD,IAAI,WAAW,6LAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QAC/B,SAAS;YACP,SAAS,QAAQ,OAAO;QAC1B;IACF;IAEA,MAAM,WAAW,CAAA,GAAA,iLAAA,CAAA,qBAAkB,AAAD,6HAGhC;QACE,SAAS;YACP;gBACE,OAAO,QAAQ,OAAO,CAAC,MAAM;YAC/B;YACA,QAAO,YAAY;gBACjB,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,GAAK,QAAQ,OAAO,CAAC,GAAG,CAAC,MAAM;gBACpE,WAAW,6LAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBAC3B;gBACF;gBACA,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,SAAS,OAAO,CAAC,GAAG,CAAC,MAAM,OAAO;YAEtC;QACF;IACF;IAGF,8DAA8D;IAC9D,MAAM,SAAS,IAAI,CAAC,OAAO;IAE3B,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACf,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,+CAA+C;IAC/C,MAAM,kBAAkB;QAAC;QAAc;QAAU;QAAiB;QAAU;KAAW;IACvF,MAAM,aAAa;QAAC;QAAe;KAAiB;IAEpD,MAAM,mBAAmB,gBAAgB,IAAI,CAAC,CAAA,QAC5C,QAAQ,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC;IAEtC,MAAM,cAAc,WAAW,IAAI,CAAC,CAAA,QAClC,QAAQ,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC;IAGtC,wEAAwE;IACxE,IAAI,oBAAoB,CAAC,MAAM;QAC7B,MAAM,cAAc,IAAI,IAAI,eAAe,QAAQ,GAAG;QACtD,YAAY,YAAY,CAAC,GAAG,CAAC,cAAc,QAAQ,OAAO,CAAC,QAAQ;QACnE,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;IAC/B;IAEA,qEAAqE;IACrE,IAAI,eAAe,MAAM;QACvB,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,cAAc,QAAQ,GAAG;IAChE;IAEA,2CAA2C;IAC3C,IAAI,QAAQ,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,aAAa,MAAM;QACzD,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,MAAM,SAC7B,IAAI,CAAC,YACL,MAAM,CAAC,QACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;QAET,IAAI,CAAC,WAAW,CAAC;YAAC;YAAS;SAAU,CAAC,QAAQ,CAAC,QAAQ,IAAI,GAAG;YAC5D,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,cAAc,QAAQ,GAAG;QAChE;IACF;IAEA,OAAO;AACT;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;KAMC,GACD;KACD;AACH"}}]}