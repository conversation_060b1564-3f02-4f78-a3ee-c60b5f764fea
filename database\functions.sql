-- Database functions for LibyanoEx
-- These functions provide business logic and utility operations

-- Function to generate unique reference numbers
CREATE OR REPLACE FUNCTION generate_reference_number()
RETURNS TEXT AS $$
DECLARE
    timestamp_part TEXT;
    random_part TEXT;
    reference_number TEXT;
BEGIN
    -- Get current timestamp in base36
    timestamp_part := UPPER(ENCODE(INT8SEND(EXTRACT(EPOCH FROM NOW())::BIGINT), 'base64'));
    
    -- Generate random string
    random_part := UPPER(SUBSTRING(MD5(RANDOM()::TEXT) FROM 1 FOR 6));
    
    -- Combine parts
    reference_number := 'LBX-' || timestamp_part || '-' || random_part;
    
    RETURN reference_number;
END;
$$ LANGUAGE plpgsql;

-- Function to generate account numbers
CREATE OR REPLACE FUNCTION generate_account_number(account_type_param TEXT, user_id_param UUID)
RETURNS TEXT AS $$
DECLARE
    type_prefix TEXT;
    user_short TEXT;
    random_digits TEXT;
    account_number TEXT;
BEGIN
    -- Set prefix based on account type
    CASE account_type_param
        WHEN 'checking' THEN type_prefix := 'CHK';
        WHEN 'savings' THEN type_prefix := 'SAV';
        WHEN 'business' THEN type_prefix := 'BUS';
        WHEN 'crypto' THEN type_prefix := 'CRY';
        ELSE type_prefix := 'GEN';
    END CASE;
    
    -- Get short version of user ID (first 8 characters)
    user_short := UPPER(SUBSTRING(user_id_param::TEXT FROM 1 FOR 8));
    
    -- Generate random 4-digit number
    random_digits := LPAD(FLOOR(RANDOM() * 10000)::TEXT, 4, '0');
    
    -- Combine parts
    account_number := type_prefix || user_short || random_digits;
    
    RETURN account_number;
END;
$$ LANGUAGE plpgsql;

-- Function to update account balance
CREATE OR REPLACE FUNCTION update_account_balance(
    account_id_param UUID,
    amount_param DECIMAL,
    operation_param TEXT -- 'add' or 'subtract'
)
RETURNS BOOLEAN AS $$
DECLARE
    current_balance DECIMAL;
    new_balance DECIMAL;
BEGIN
    -- Get current balance
    SELECT balance INTO current_balance
    FROM public.accounts
    WHERE id = account_id_param;
    
    IF current_balance IS NULL THEN
        RAISE EXCEPTION 'Account not found';
    END IF;
    
    -- Calculate new balance
    IF operation_param = 'add' THEN
        new_balance := current_balance + amount_param;
    ELSIF operation_param = 'subtract' THEN
        new_balance := current_balance - amount_param;
        
        -- Check for sufficient funds
        IF new_balance < 0 THEN
            RAISE EXCEPTION 'Insufficient funds';
        END IF;
    ELSE
        RAISE EXCEPTION 'Invalid operation. Use "add" or "subtract"';
    END IF;
    
    -- Update balance
    UPDATE public.accounts
    SET balance = new_balance, updated_at = NOW()
    WHERE id = account_id_param;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Function to process transaction
CREATE OR REPLACE FUNCTION process_transaction(transaction_id_param UUID)
RETURNS BOOLEAN AS $$
DECLARE
    transaction_record RECORD;
    from_account_id UUID;
    to_account_id UUID;
BEGIN
    -- Get transaction details
    SELECT * INTO transaction_record
    FROM public.transactions
    WHERE id = transaction_id_param;
    
    IF transaction_record IS NULL THEN
        RAISE EXCEPTION 'Transaction not found';
    END IF;
    
    IF transaction_record.status != 'pending' THEN
        RAISE EXCEPTION 'Transaction is not in pending status';
    END IF;
    
    -- Process based on transaction type
    CASE transaction_record.type
        WHEN 'deposit' THEN
            -- Find user's primary account
            SELECT id INTO from_account_id
            FROM public.accounts
            WHERE user_id = transaction_record.user_id
            AND account_type = 'checking'
            LIMIT 1;
            
            -- Add amount to account
            PERFORM update_account_balance(from_account_id, transaction_record.amount, 'add');
            
        WHEN 'withdrawal' THEN
            -- Find user's primary account
            SELECT id INTO from_account_id
            FROM public.accounts
            WHERE user_id = transaction_record.user_id
            AND account_type = 'checking'
            LIMIT 1;
            
            -- Subtract amount from account
            PERFORM update_account_balance(from_account_id, transaction_record.amount, 'subtract');
            
        WHEN 'transfer' THEN
            -- This would require from_account and to_account logic
            -- Implementation depends on how accounts are referenced
            NULL;
            
        ELSE
            RAISE EXCEPTION 'Unsupported transaction type';
    END CASE;
    
    -- Update transaction status
    UPDATE public.transactions
    SET status = 'completed', processed_at = NOW(), updated_at = NOW()
    WHERE id = transaction_id_param;
    
    -- Log the transaction
    INSERT INTO public.transaction_logs (transaction_id, action, old_status, new_status, performed_by)
    VALUES (transaction_id_param, 'processed', 'pending', 'completed', auth.uid());
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Function to get user statistics
CREATE OR REPLACE FUNCTION get_user_stats(user_id_param UUID)
RETURNS JSON AS $$
DECLARE
    total_transactions INTEGER;
    total_amount DECIMAL;
    account_count INTEGER;
    total_balance DECIMAL;
    result JSON;
BEGIN
    -- Count transactions
    SELECT COUNT(*), COALESCE(SUM(amount), 0)
    INTO total_transactions, total_amount
    FROM public.transactions
    WHERE user_id = user_id_param AND status = 'completed';
    
    -- Count accounts and total balance
    SELECT COUNT(*), COALESCE(SUM(balance), 0)
    INTO account_count, total_balance
    FROM public.accounts
    WHERE user_id = user_id_param AND is_active = true;
    
    -- Build result JSON
    result := json_build_object(
        'total_transactions', total_transactions,
        'total_amount', total_amount,
        'account_count', account_count,
        'total_balance', total_balance,
        'generated_at', NOW()
    );
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- Function to create notification
CREATE OR REPLACE FUNCTION create_notification(
    user_id_param UUID,
    title_param TEXT,
    message_param TEXT,
    type_param TEXT DEFAULT 'info',
    action_url_param TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    notification_id UUID;
BEGIN
    INSERT INTO public.notifications (user_id, title, message, type, action_url)
    VALUES (user_id_param, title_param, message_param, type_param, action_url_param)
    RETURNING id INTO notification_id;
    
    RETURN notification_id;
END;
$$ LANGUAGE plpgsql;
