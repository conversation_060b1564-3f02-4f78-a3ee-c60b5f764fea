import { create } from 'zustand'
import { DashboardStats, Transaction } from '@/types'

interface DashboardState {
  stats: DashboardStats | null
  recentTransactions: Transaction[]
  isLoading: boolean
  setStats: (stats: DashboardStats) => void
  setRecentTransactions: (transactions: Transaction[]) => void
  setLoading: (loading: boolean) => void
}

export const useDashboardStore = create<DashboardState>((set) => ({
  stats: null,
  recentTransactions: [],
  isLoading: true,
  setStats: (stats) => set({ stats }),
  setRecentTransactions: (recentTransactions) => set({ recentTransactions }),
  setLoading: (isLoading) => set({ isLoading }),
}))
