'use client'

import { useAuth } from '@/hooks/use-auth'
import DashboardLayout from '@/components/layout/dashboard-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { 
  ArrowUpRight, 
  ArrowDownRight, 
  DollarSign, 
  Users, 
  CreditCard, 
  TrendingUp,
  Plus
} from 'lucide-react'

export default function DashboardPage() {
  const { user } = useAuth()

  // Mock data - in real app, this would come from API
  const stats = [
    {
      title: 'Total Balance',
      value: '$12,345.67',
      change: '+2.5%',
      changeType: 'positive' as const,
      icon: DollarSign,
    },
    {
      title: 'Total Transactions',
      value: '1,234',
      change: '+12%',
      changeType: 'positive' as const,
      icon: CreditCard,
    },
    {
      title: 'Active Users',
      value: '567',
      change: '+5.2%',
      changeType: 'positive' as const,
      icon: Users,
    },
    {
      title: 'Monthly Growth',
      value: '23.5%',
      change: '+1.2%',
      changeType: 'positive' as const,
      icon: TrendingUp,
    },
  ]

  const recentTransactions = [
    {
      id: '1',
      type: 'deposit',
      amount: 1500.00,
      currency: 'USD',
      description: 'Salary deposit',
      date: '2025-01-10',
      status: 'completed',
    },
    {
      id: '2',
      type: 'withdrawal',
      amount: 250.00,
      currency: 'USD',
      description: 'ATM withdrawal',
      date: '2025-01-09',
      status: 'completed',
    },
    {
      id: '3',
      type: 'transfer',
      amount: 500.00,
      currency: 'USD',
      description: 'Transfer to savings',
      date: '2025-01-08',
      status: 'pending',
    },
  ]

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Welcome section */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Welcome back, {user?.full_name || user?.email}!
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Here's what's happening with your account today.
          </p>
        </div>

        {/* Stats grid */}
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
          {stats.map((stat) => {
            const Icon = stat.icon
            return (
              <Card key={stat.title}>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    {stat.title}
                  </CardTitle>
                  <Icon className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stat.value}</div>
                  <p className="text-xs text-muted-foreground">
                    <span className={stat.changeType === 'positive' ? 'text-green-600' : 'text-red-600'}>
                      {stat.change}
                    </span>
                    {' '}from last month
                  </p>
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* Quick actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>
              Common tasks you can perform
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
              <Button className="h-20 flex-col space-y-2">
                <Plus className="h-6 w-6" />
                <span>New Transaction</span>
              </Button>
              <Button variant="outline" className="h-20 flex-col space-y-2">
                <ArrowUpRight className="h-6 w-6" />
                <span>Send Money</span>
              </Button>
              <Button variant="outline" className="h-20 flex-col space-y-2">
                <ArrowDownRight className="h-6 w-6" />
                <span>Request Money</span>
              </Button>
              <Button variant="outline" className="h-20 flex-col space-y-2">
                <CreditCard className="h-6 w-6" />
                <span>View Accounts</span>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Recent transactions */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Transactions</CardTitle>
            <CardDescription>
              Your latest transaction activity
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentTransactions.map((transaction) => (
                <div
                  key={transaction.id}
                  className="flex items-center justify-between p-4 border rounded-lg"
                >
                  <div className="flex items-center space-x-4">
                    <div className={`p-2 rounded-full ${
                      transaction.type === 'deposit' 
                        ? 'bg-green-100 text-green-600' 
                        : transaction.type === 'withdrawal'
                        ? 'bg-red-100 text-red-600'
                        : 'bg-blue-100 text-blue-600'
                    }`}>
                      {transaction.type === 'deposit' ? (
                        <ArrowDownRight className="h-4 w-4" />
                      ) : transaction.type === 'withdrawal' ? (
                        <ArrowUpRight className="h-4 w-4" />
                      ) : (
                        <CreditCard className="h-4 w-4" />
                      )}
                    </div>
                    <div>
                      <p className="font-medium">{transaction.description}</p>
                      <p className="text-sm text-gray-500">{transaction.date}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className={`font-medium ${
                      transaction.type === 'deposit' 
                        ? 'text-green-600' 
                        : 'text-gray-900 dark:text-white'
                    }`}>
                      {transaction.type === 'deposit' ? '+' : '-'}
                      ${transaction.amount.toFixed(2)}
                    </p>
                    <p className={`text-sm ${
                      transaction.status === 'completed' 
                        ? 'text-green-600' 
                        : 'text-yellow-600'
                    }`}>
                      {transaction.status}
                    </p>
                  </div>
                </div>
              ))}
            </div>
            <div className="mt-4 text-center">
              <Button variant="outline">View All Transactions</Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
